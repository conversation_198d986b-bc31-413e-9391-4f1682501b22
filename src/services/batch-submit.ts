import { gdRequest } from './request';
import {
  IBatchSubmitShopListParams,
  IBatchSubmitShopListResponse,
  IQueryUnfinishedOptWoosBizOrderParams,
  IUnfinishedOptWoosBizOrderResponse,
  IBatchSubmitAndCreateEspOrderParams,
  IBatchSubmitAndCreateEspOrderResponse,
  IQueryShopCollectInfoParams,
  IShopCollectInfoResponse,
} from '@/types/batch-submit';

// 运维工单门店信息查询（新增接口）
export const getBatchSubmitShopList = (
  params: IBatchSubmitShopListParams,
): Promise<IBatchSubmitShopListResponse> => {
  return gdRequest('amap-sales-operation.AgentOperationQueryFacade.queryOperationOrderShopList', {
    ...params,
    queryType: 'OptOrderShopInfo', // 查询类型固定为运维工单门店信息
    includeShopTaskProcess: true, // 包含门店任务进度
    appSource: 'xy-client', // 应用来源
    sortBy: 'LAST_SUBMIT_TIME', // 默认按最近提报时间排序
    sortType: 'desc', // 降序排列
  });
};

// 查询批量提报未完结流水
export const queryUnfinishedOptWoosBizOrder = (
  params: IQueryUnfinishedOptWoosBizOrderParams,
): Promise<IUnfinishedOptWoosBizOrderResponse> => {
  return gdRequest(
    'amap-sales-operation.ShopInfraManageFacade.queryUnfinishedOptWoosBizOrder',
    params,
  );
};

// 批量提报任务创建
export const batchSubmitAndCreateEspOrder = (
  params: IBatchSubmitAndCreateEspOrderParams,
): Promise<IBatchSubmitAndCreateEspOrderResponse> => {
  return gdRequest(
    'amap-sales-operation.ShopInfraManageFacade.batchSubmitAndCreateEspOrder',
    params,
  );
};

// 装修素材提报记录查询（复用现有接口）
export const queryShopCollectInfo = (
  params: IQueryShopCollectInfoParams,
): Promise<IShopCollectInfoResponse> => {
  return gdRequest('amap-sales-operation.ShopInfraManageFacade.queryShopCollectInfo', params);
};
